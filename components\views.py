from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from .models import (
    ComponentTypeMain, ComponentTypeSub, ComponentType, ComponentGroup,
    Component, StandardComponent, ManufacturerComponent, TestingCenterComponent
)
from testings.models import Service
from .serializers import (
    ComponentTypeMainSerializer, ComponentTypeSubSerializer, ComponentTypeSerializer,
    ComponentGroupSerializer, ComponentSerializer, StandardComponentSerializer,
    ManufacturerComponentSerializer, TestingCenterComponentSerializer
)
from testings.serializers import ServiceSerializer
from utils.pagination import CustomPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from rest_framework.decorators import action
from rest_framework.response import Response
from utils.util import convert_str_to_bool
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from django.http import HttpResponse
from rest_framework.permissions import AllowAny
from rest_framework import status
from users.models import User
from django.db.models import Q

@extend_schema(
    tags=["Components"]
)
class ComponentTypeMainViewSet(viewsets.ModelViewSet):
    queryset = ComponentTypeMain.objects.all()
    serializer_class = ComponentTypeMainSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [MultiPartParser, FormParser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'status']

    def list(self, request, *args, **kwargs):
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        if page is not None:
            for item in page:
                item.componentTypeSubs = ComponentTypeSub.objects.filter(componentTypeMain=item)
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['POST'], url_path='upload-icon')
    def upload_icon(self, request, pk=None):
        """
        Upload icon file for ComponentTypeMain
        """
        instance = self.get_object()
        if 'iconfile' in request.FILES:
            instance.iconfile = request.FILES['iconfile']
            instance.save()
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(
            {"error": "No icon file provided"},
            status=status.HTTP_400_BAD_REQUEST
        )


@extend_schema(
    tags=["Components"]
)
class ComponentTypeSubViewSet(viewsets.ModelViewSet):
    queryset = ComponentTypeSub.objects.all()
    serializer_class = ComponentTypeSubSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'status', 'componentTypeMain__id']

    def list(self, request, *args, **kwargs):
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        componentTypeMainId = request.query_params.get('componentTypeMain__id')
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        if componentTypeMainId:
            queryset = queryset.filter(componentTypeMain__id=componentTypeMainId)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema(
    tags=["Components"]
)
class ComponentTypeViewSet(viewsets.ModelViewSet):
    queryset = ComponentType.objects.all()
    serializer_class = ComponentTypeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'status', 'componentTypeMain__id', 'componentTypeSub__id']

    def list(self, request, *args, **kwargs):
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        componentTypeMainId = request.query_params.get('componentTypeMain__id')
        componentTypeSubId = request.query_params.get('componentTypeSub__id')
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        if componentTypeMainId:
            queryset = queryset.filter(componentTypeMain__id=componentTypeMainId)
        if componentTypeSubId:
            queryset = queryset.filter(componentTypeSub__id=componentTypeSubId)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema(
    tags=["Components"]
)
class ComponentGroupViewSet(viewsets.ModelViewSet):
    queryset = ComponentGroup.objects.all()
    serializer_class = ComponentGroupSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'status', 'componentTypeMain__id', 'componentTypeSub__id', 'componentType__id']

    def list(self, request, *args, **kwargs):
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        componentTypeMainId = request.query_params.get('componentTypeMain__id')
        componentTypeSubId = request.query_params.get('componentTypeSub__id')
        componentTypeId = request.query_params.get('componentType__id')
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        if componentTypeMainId:
            queryset = queryset.filter(componentTypeMain__id=componentTypeMainId)
        if componentTypeSubId:
            queryset = queryset.filter(componentTypeSub__id=componentTypeSubId)
        if componentTypeId:
            queryset = queryset.filter(componentType__id=componentTypeId)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema(
    tags=["Components"]
)
class ComponentViewSet(viewsets.ModelViewSet):
    queryset = Component.objects.all()
    serializer_class = ComponentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = [
        'componentTypeMain__id', 'componentTypeSub__id', 'componentType__id',
        'componentGroup__id', 'status', 'majorSubSystem', 'majorComponents', 'subComponents'
    ]
    ordering_fields = ['componentGroup__id', 'id']

    def list(self, request, *args, **kwargs):
        componentTypeMainId = request.query_params.get('componentTypeMain__id')
        componentTypeSubId = request.query_params.get('componentTypeSub__id')
        componentGroup__id = request.query_params.get('componentGroup__id')
        componentType__id = request.query_params.get('componentType__id')
        majorSubSystem = request.query_params.get('majorSubSystem')
        majorComponents = request.query_params.get('majorComponents')
        subComponents = request.query_params.get('subComponents')
        standardId = request.query_params.get('standard__id')
        testingCenterId = request.query_params.get('testingCenter__id')
        manufacturerId = request.query_params.get('manufacturer__id')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        queryset = self.get_queryset()
        if componentTypeMainId:
            queryset = queryset.filter(componentTypeMain__id=componentTypeMainId)
        if componentTypeSubId:
            queryset = queryset.filter(componentTypeSub__id=componentTypeSubId)
        if componentGroup__id:
            queryset = queryset.filter(componentGroup__id=componentGroup__id)
        if componentType__id:
            queryset = queryset.filter(componentType__id=componentType__id)
        if majorSubSystem:
            queryset = queryset.filter(majorSubSystem__icontains=majorSubSystem)
        if majorComponents:
            queryset = queryset.filter(majorComponents__icontains=majorComponents)
        if subComponents:
            queryset = queryset.filter(subComponents__icontains=subComponents)
        if standardId:
            standardComponents = StandardComponent.objects.filter(standard__id=standardId)
            componentIds = standardComponents.values_list('component__id', flat=True)
            queryset = queryset.filter(id__in=componentIds)
        if testingCenterId:
            testingCenterComponents = TestingCenterComponent.objects.filter(testingCenter__id=testingCenterId)
            componentIds = testingCenterComponents.values_list('component__id', flat=True)
            queryset = queryset.filter(id__in=componentIds)
        if manufacturerId:
            manufacturerComponents = ManufacturerComponent.objects.filter(manufacturer__id=manufacturerId)
            componentIds = manufacturerComponents.values_list('component__id', flat=True)
            queryset = queryset.filter(id__in=componentIds)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @DeprecationWarning
    @action(detail=False, methods=['GET'], url_path='list-group')
    def list_group(self, request):
        names = list(self.queryset.values_list('name', flat=True).distinct())
        return Response(names)

    @action(detail=False, methods=['GET'], url_path='list-major-component')
    def list_major_component(self, request):
        names = list(self.queryset.values_list('majorComponents', flat=True).distinct())
        names = [name for name in names if (name is not None and name != '' and name != '-')]
        return Response(names)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        Download manufacturer data as Excel file
        Request Body:
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        """
        
        ids = request.data.get('ids')        
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
            
        for item in queryset:
            try:
                componentType = ComponentType.objects.get(id=item.componentType.id)
                item.componentTypeName = componentType.name
            except ComponentType.DoesNotExist:
                item.componentType = None
        

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'Component Group': item.componentGroup.name if item.componentGroup else '',
                'Component Type': item.componentType.name if item.componentType else '',
                'Major Sub-system': item.majorSubSystem,
                'Major Components': item.majorComponents,
                'Sub-Components': item.subComponents,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรถไฟชิ้นส่วน และอุปกรณ์'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:E1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:E2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
   
      
    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลรถไฟชิ้นส่วน และอุปกรณ์</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Component IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'Component Group': item.componentGroup.name if item.componentGroup else '',
                'Component Type': item.componentType.name if item.componentType else '',
                'Major Sub-system': item.majorSubSystem,
                'Major Components': item.majorComponents,
                'Sub-Components': item.subComponents,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรถไฟชิ้นส่วน และอุปกรณ์'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:I1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:I2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Components"]
)
class StandardComponentViewSet(viewsets.ModelViewSet):
    queryset = StandardComponent.objects.all()
    serializer_class = StandardComponentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['standard__id', 'component__id']
    
    def list(self, request, *args, **kwargs):
        standard__id = request.query_params.get('standard__id')
        component__id = request.query_params.get('component__id')
        queryset = self.get_queryset()
        
        if standard__id:
            queryset = queryset.filter(standard__id=standard__id)
        if component__id:
            queryset = queryset.filter(component__id=component__id)
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='save-list')
    def save_list(self, request, *args, **kwargs):
        created_items = []
        for item in request.data:
            if not StandardComponent.objects.filter(standard__id=item['standardId'], component__id=item['componentId']).exists():
                created_items.append(item)
        
        if created_items:
            serializer = self.get_serializer(data=created_items, many=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response({"message": "No new components were created. All items already exist."}, status=status.HTTP_204_NO_CONTENT)


@extend_schema(
    tags=["Components"]
)
class TestingCenterComponentViewSet(viewsets.ModelViewSet):
    queryset = TestingCenterComponent.objects.all()
    serializer_class = TestingCenterComponentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['testingCenter__id', 'component__id']
    
    def list(self, request, *args, **kwargs):
        testingCenter__id = request.query_params.get('testingCenter__id')
        component__id = request.query_params.get('component__id')
        queryset = self.get_queryset()
        
        if testingCenter__id:
            queryset = queryset.filter(testingCenter__id=testingCenter__id)
        if component__id:
            queryset = queryset.filter(component__id=component__id)
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            for item in serializer.data:
                # Extract the testingCenter ID instead of using the whole object
                testing_center_id = item['testingCenter']['id'] if isinstance(item['testingCenter'], dict) else item['testingCenter']
                services = Service.objects.filter(testingCenter_id=testing_center_id)
                item['testingCenter']['services'] = ServiceSerializer(services, many=True).data
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['POST'], url_path='save-list')
    def save_list(self, request, *args, **kwargs):
        created_items = []
        for item in request.data:
            if not TestingCenterComponent.objects.filter(testingCenter__id=item['testingCenterId'], component__id=item['componentId']).exists():
                created_items.append(item)
        
        if created_items:
            serializer = self.get_serializer(data=created_items, many=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response({"message": "No new components were created. All items already exist."}, status=status.HTTP_204_NO_CONTENT)


@extend_schema(
    tags=["Components"]
)
class ManufacturerComponentViewSet(viewsets.ModelViewSet):
    queryset = ManufacturerComponent.objects.all()
    serializer_class = ManufacturerComponentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['manufacturer__id', 'component__id', 'type']
    
    def list(self, request, *args, **kwargs):
        manufacturer__id = request.query_params.get('manufacturer__id')
        component__id = request.query_params.get('component__id')
        type = request.query_params.get('type')
        queryset = self.get_queryset()
        
        if manufacturer__id:
            queryset = queryset.filter(manufacturer__id=manufacturer__id)
        if component__id:
            queryset = queryset.filter(component__id=component__id)
        if type:
            queryset = queryset.filter(type=type)
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['POST'], url_path='save-list')
    def save_list(self, request, *args, **kwargs):
        created_items = []
        for item in request.data:
            if not ManufacturerComponent.objects.filter(manufacturer__id=item['manufacturerId'], component__id=item['componentId'], type=item['type']).exists():
                created_items.append(item)
        
        if created_items:
            serializer = self.get_serializer(data=created_items, many=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response({"message": "No new components were created. All items already exist."}, status=status.HTTP_204_NO_CONTENT)